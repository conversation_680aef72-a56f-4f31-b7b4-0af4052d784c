<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背单词小程序</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .main-content {
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 2px solid #e1f5fe;
        }

        .section h2 {
            color: #0277bd;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 3px solid #4fc3f7;
            padding-bottom: 10px;
        }

        .input-area {
            grid-column: 1 / -1;
            transition: all 0.3s ease-in-out;
        }

        textarea {
            width: 100%;
            height: 120px;
            border: 2px solid #b3e5fc;
            border-radius: 10px;
            padding: 15px;
            font-size: 16px;
            resize: vertical;
            transition: border-color 0.3s;
        }

        textarea:focus {
            outline: none;
            border-color: #29b6f6;
            box-shadow: 0 0 10px rgba(41, 182, 246, 0.3);
        }

        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        button {
            background: linear-gradient(135deg, #29b6f6 0%, #0277bd 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(41, 182, 246, 0.3);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(41, 182, 246, 0.4);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .mode-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .mode-button {
            background: linear-gradient(135deg, #81c784 0%, #4caf50 100%);
        }

        .mode-button.active {
            background: linear-gradient(135deg, #ff7043 0%, #f4511e 100%);
        }

        .study-area {
            text-align: center;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .question {
            font-size: 2em;
            color: #0277bd;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .answer-input {
            width: 100%;
            padding: 15px;
            font-size: 18px;
            border: 2px solid #b3e5fc;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }

        .choices {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .choice-button {
            background: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%);
            color: #0277bd;
            border: 2px solid #4fc3f7;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }

        .choice-button:hover {
            background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
            color: white;
        }

        .typing-area {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .letter-input {
            width: 40px;
            height: 50px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            border: 2px solid #b3e5fc;
            border-radius: 8px;
            background: white;
        }

        .letter-input:focus {
            border-color: #29b6f6;
            box-shadow: 0 0 5px rgba(41, 182, 246, 0.5);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #81c784;
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #2e7d32;
        }

        .stat-label {
            color: #4caf50;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .feedback {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
        }

        .feedback.correct {
            background: linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%);
            color: #2e7d32;
            border: 2px solid #4caf50;
        }

        .feedback.incorrect {
            background: linear-gradient(135deg, #ffcdd2 0%, #ef9a9a 100%);
            color: #c62828;
            border: 2px solid #f44336;
        }

        .word-list {
            max-height: 300px;
            overflow-y: auto;
            border: 2px solid #e1f5fe;
            border-radius: 10px;
            padding: 15px;
        }

        .word-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #e1f5fe;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            margin-bottom: 8px;
            border-radius: 8px;
        }

        .word-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .word-content {
            flex: 1;
        }

        .word-chinese {
            font-weight: bold;
            color: #0277bd;
        }

        .word-english {
            color: #666;
            margin-left: 10px;
        }

        .error-count {
            background: #ffcdd2;
            color: #c62828;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 10px;
        }

        .delete-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 5px;
        }

        .show-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
            transition: all 0.3s;
        }

        .show-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        .speak-btn {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
        }

        .speak-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
        }

        .speak-btn:active {
            transform: translateY(0);
        }

        .speak-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .file-input {
            display: none;
        }

        .file-label {
            background: linear-gradient(135deg, #ab47bc 0%, #8e24aa 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            display: inline-block;
            font-weight: bold;
            transition: all 0.3s;
        }

        .file-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(171, 71, 188, 0.4);
        }

        .word-count {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            color: #ef6c00;
            padding: 10px 15px;
            border-radius: 20px;
            font-weight: bold;
            border: 2px solid #ffb74d;
            margin-bottom: 15px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .button-group {
                justify-content: center;
            }

            .stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .choices {
                grid-template-columns: 1fr;
            }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 背单词小程序</h1>
            <p>智能学习，轻松记忆</p>
        </div>

        <div class="main-content">
            <!-- 词汇输入区域 -->
            <div class="section input-area" id="wordManagementSection">
                <h2>📝 词汇管理</h2>
                <div class="word-count" id="wordCount">当前词表：0 个单词</div>
                <textarea id="wordInput" placeholder="请输入单词对，支持格式：&#10;苹果 apple&#10;香蕉,banana&#10;橙子 orange"></textarea>
                <div class="button-group">
                    <button onclick="loadWords()">📚 加载词表</button>
                    <button onclick="saveWordList()">💾 保存词表</button>
                    <button onclick="showSavedLists()">📋 管理词表</button>
                    <label for="importFile" class="file-label">📁 导入文件</label>
                    <input type="file" id="importFile" class="file-input" accept=".json" onchange="importWords()">
                    <button onclick="exportWords()">📤 导出词表</button>
                </div>
            </div>

            <!-- 学习模式选择 -->
            <div class="section">
                <h2>🎮 学习模式</h2>
                <div class="mode-buttons">
                    <button class="mode-button active" onclick="setMode('default')">默认模式</button>
                    <button class="mode-button" onclick="setMode('choice')">选择题</button>
                    <button class="mode-button" onclick="setMode('typing')">打字练习</button>
                </div>
                <button onclick="startStudy()" id="startBtn">🚀 开始学习</button>
                <button onclick="stopStudy()" id="stopBtn" style="display: none;">⏹️ 停止练习</button>
                <button onclick="resetStats()" id="resetBtn">🔄 重新开始</button>
            </div>

            <!-- 学习区域 -->
            <div class="section">
                <h2>📖 学习区域</h2>
                <div class="study-area" id="studyArea">
                    <p>请先加载词表，然后选择学习模式开始学习</p>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="section">
                <h2>📊 学习统计</h2>
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-value" id="totalCount">0</div>
                        <div class="stat-label">总题数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="correctCount">0</div>
                        <div class="stat-label">正确数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="wrongCount">0</div>
                        <div class="stat-label">错误数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="accuracy">0%</div>
                        <div class="stat-label">正确率</div>
                    </div>
                </div>
            </div>

            <!-- 错词本 -->
            <div class="section">
                <h2>❌ 错词本</h2>
                <div class="button-group">
                    <button onclick="practiceWrongWords()">📚 练习错词</button>
                    <button onclick="toggleAllEnglish()">👁️ 全部显示/隐藏</button>
                    <button onclick="testVoice()">🔊 测试语音</button>
                    <button onclick="clearWrongWords()">🗑️ 清空错词本</button>
                    <button onclick="exportWrongWords()">📤 导出错词本</button>
                </div>
                <div class="word-list" id="wrongWordsList">
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentWords = [];
        let currentMode = 'default';
        let currentQuestion = null;
        let currentIndex = 0;
        let stats = { total: 0, correct: 0, wrong: 0 };
        let wrongWords = JSON.parse(localStorage.getItem('wrongWords') || '[]');
        let isStudying = false;
        let practicedWords = []; // 跟踪已练习的单词

        // 语音朗读功能
        let speechSynthesis = window.speechSynthesis;
        let isVoiceSupported = 'speechSynthesis' in window;

        function speakWord(word) {
            if (!isVoiceSupported) {
                alert('您的浏览器不支持语音功能！');
                return;
            }

            // 停止当前正在播放的语音
            speechSynthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(word);

            // 设置语音参数
            utterance.lang = 'en-US'; // 英语
            utterance.rate = 0.8; // 语速稍慢一些
            utterance.pitch = 1; // 音调
            utterance.volume = 1; // 音量

            // 尝试使用英语语音
            const voices = speechSynthesis.getVoices();
            const englishVoice = voices.find(voice =>
                voice.lang.includes('en') && voice.lang.includes('US')
            ) || voices.find(voice => voice.lang.includes('en'));

            if (englishVoice) {
                utterance.voice = englishVoice;
            }

            speechSynthesis.speak(utterance);
        }

        function testVoice() {
            if (!isVoiceSupported) {
                alert('您的浏览器不支持语音功能！\n\n支持语音功能的浏览器：\n• Chrome\n• Firefox\n• Safari\n• Edge');
                return;
            }

            const testWords = ['hello', 'apple', 'banana', 'orange', 'computer'];
            const randomWord = testWords[Math.floor(Math.random() * testWords.length)];

            speakWord(randomWord);

            // 显示测试信息
            const message = `🔊 正在播放测试单词："${randomWord}"\n\n如果听不到声音，请检查：\n• 设备音量是否开启\n• 浏览器是否允许音频播放\n• 是否有可用的英语语音包`;

            setTimeout(() => {
                alert(message);
            }, 500);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateWordCount();
            updateStatsDisplay();
            updateWrongWordsList();

            // 加载语音列表
            if (isVoiceSupported) {
                speechSynthesis.onvoiceschanged = function() {
                    // 语音列表加载完成
                };
            }
        });

        function updateStatsDisplay() {
            document.getElementById('totalCount').textContent = stats.total;
            document.getElementById('correctCount').textContent = stats.correct;
            document.getElementById('wrongCount').textContent = stats.wrong;
            const accuracy = stats.total > 0 ? Math.round((stats.correct / stats.total) * 100) : 0;
            document.getElementById('accuracy').textContent = accuracy + '%';
        }

        // 词汇管理功能
        function loadWords() {
            const input = document.getElementById('wordInput').value.trim();
            if (!input) {
                alert('请先输入单词对！');
                return;
            }

            currentWords = [];
            const lines = input.split('\n');

            lines.forEach(line => {
                line = line.trim();
                if (line) {
                    let chinese, english;
                    if (line.includes(',')) {
                        [chinese, english] = line.split(',').map(s => s.trim());
                    } else if (line.includes(' ')) {
                        const parts = line.split(' ');
                        chinese = parts[0].trim();
                        english = parts.slice(1).join(' ').trim();
                    }

                    if (chinese && english) {
                        currentWords.push({ chinese, english });
                    }
                }
            });

            updateWordCount();
            alert(`成功加载 ${currentWords.length} 个单词对！`);
        }

        function updateWordCount() {
            document.getElementById('wordCount').textContent = `当前词表：${currentWords.length} 个单词`;
        }

        function saveWordList() {
            if (currentWords.length === 0) {
                alert('请先加载词表！');
                return;
            }

            const name = prompt('请输入词表名称：');
            if (!name) return;

            const savedLists = JSON.parse(localStorage.getItem('savedWordLists') || '{}');
            savedLists[name] = currentWords;
            localStorage.setItem('savedWordLists', JSON.stringify(savedLists));
            alert('词表保存成功！');
        }

        function showSavedLists() {
            const savedLists = JSON.parse(localStorage.getItem('savedWordLists') || '{}');
            const names = Object.keys(savedLists);

            if (names.length === 0) {
                alert('暂无保存的词表！');
                return;
            }

            let message = '已保存的词表：\n\n';
            names.forEach((name, index) => {
                message += `${index + 1}. ${name} (${savedLists[name].length}个单词)\n`;
            });
            message += '\n请输入要加载的词表编号，或输入"delete:编号"删除词表：';

            const input = prompt(message);
            if (!input) return;

            if (input.startsWith('delete:')) {
                const deleteIndex = parseInt(input.split(':')[1]) - 1;
                if (deleteIndex >= 0 && deleteIndex < names.length) {
                    const nameToDelete = names[deleteIndex];
                    delete savedLists[nameToDelete];
                    localStorage.setItem('savedWordLists', JSON.stringify(savedLists));
                    alert(`词表"${nameToDelete}"已删除！`);
                }
            } else {
                const index = parseInt(input) - 1;
                if (index >= 0 && index < names.length) {
                    const selectedName = names[index];
                    currentWords = savedLists[selectedName];
                    document.getElementById('wordInput').value = currentWords.map(w => `${w.chinese} ${w.english}`).join('\n');
                    updateWordCount();
                    alert(`词表"${selectedName}"加载成功！`);
                }
            }
        }

        function importWords() {
            const file = document.getElementById('importFile').files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    if (Array.isArray(data) && data.every(item => item.chinese && item.english)) {
                        currentWords = data;
                        document.getElementById('wordInput').value = currentWords.map(w => `${w.chinese} ${w.english}`).join('\n');
                        updateWordCount();
                        alert(`成功导入 ${currentWords.length} 个单词！`);
                    } else {
                        alert('文件格式不正确！');
                    }
                } catch (error) {
                    alert('文件解析失败！');
                }
            };
            reader.readAsText(file);
        }

        function exportWords() {
            if (currentWords.length === 0) {
                alert('当前词表为空！');
                return;
            }

            const dataStr = JSON.stringify(currentWords, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `词表_${new Date().toISOString().slice(0, 10)}.json`;
            link.click();

            URL.revokeObjectURL(url);
        }

        // 学习模式功能
        function setMode(mode) {
            currentMode = mode;
            document.querySelectorAll('.mode-button').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        function startStudy() {
            if (currentWords.length === 0) {
                alert('请先加载词表！');
                return;
            }

            isStudying = true;
            currentIndex = 0;
            practicedWords = []; // 重置已练习单词列表

            // 隐藏词汇管理区域
            hideWordManagement();

            // 切换按钮显示状态
            document.getElementById('startBtn').style.display = 'none';
            document.getElementById('stopBtn').style.display = 'inline-block';

            nextQuestion();
        }

        function stopStudy() {
            if (confirm('确定要停止当前练习吗？')) {
                isStudying = false;

                // 重新显示词汇管理区域
                showWordManagement();

                // 切换按钮显示状态
                document.getElementById('startBtn').style.display = 'inline-block';
                document.getElementById('stopBtn').style.display = 'none';

                // 重置学习区域
                document.getElementById('studyArea').innerHTML = '<p>练习已停止，请选择学习模式重新开始</p>';
            }
        }

        function hideWordManagement() {
            const wordManagementSection = document.getElementById('wordManagementSection');
            if (wordManagementSection) {
                wordManagementSection.style.opacity = '0';
                wordManagementSection.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    wordManagementSection.style.display = 'none';
                }, 300);
            }
        }

        function showWordManagement() {
            const wordManagementSection = document.getElementById('wordManagementSection');
            if (wordManagementSection) {
                wordManagementSection.style.display = 'block';
                setTimeout(() => {
                    wordManagementSection.style.opacity = '1';
                    wordManagementSection.style.transform = 'translateY(0)';
                }, 10);
            }
        }

        function nextQuestion() {
            // 检查是否所有单词都已练习过
            if (practicedWords.length >= currentWords.length) {
                finishStudy();
                return;
            }

            // 获取未练习的单词
            const unpracticedWords = currentWords.filter(word =>
                !practicedWords.some(practiced =>
                    practiced.chinese === word.chinese && practiced.english === word.english
                )
            );

            if (unpracticedWords.length === 0) {
                finishStudy();
                return;
            }

            // 从未练习的单词中随机选择一个
            const randomIndex = Math.floor(Math.random() * unpracticedWords.length);
            currentQuestion = unpracticedWords[randomIndex];

            // 将当前单词添加到已练习列表
            practicedWords.push({
                chinese: currentQuestion.chinese,
                english: currentQuestion.english
            });

            displayQuestion();
        }

        function finishStudy() {
            isStudying = false;

            // 重新显示词汇管理区域
            showWordManagement();

            // 切换按钮显示状态
            document.getElementById('startBtn').style.display = 'inline-block';
            document.getElementById('stopBtn').style.display = 'none';

            const studyArea = document.getElementById('studyArea');

            studyArea.innerHTML = `
                <div class="feedback correct" style="margin-bottom: 20px;">
                    🎉 恭喜！你已经完成了所有 ${currentWords.length} 个单词的练习！
                </div>
                <div style="text-align: center;">
                    <p style="font-size: 18px; color: #0277bd; margin-bottom: 20px;">
                        本轮练习统计：<br>
                        总题数：${stats.total} | 正确：${stats.correct} | 错误：${stats.wrong} | 正确率：${stats.total > 0 ? Math.round((stats.correct / stats.total) * 100) : 0}%
                    </p>
                    <button onclick="startStudy()" style="margin-right: 15px;">🔄 重新开始练习</button>
                    <button onclick="resetStats()">📊 重置统计</button>
                </div>
            `;

            alert(`🎉 练习完成！\n\n本轮统计：\n总题数：${stats.total}\n正确：${stats.correct}\n错误：${stats.wrong}\n正确率：${stats.total > 0 ? Math.round((stats.correct / stats.total) * 100) : 0}%`);
        }

        function displayQuestion() {
            const studyArea = document.getElementById('studyArea');
            studyArea.innerHTML = '';

            // 清除之前的反馈
            const existingFeedback = document.querySelector('.feedback');
            if (existingFeedback) {
                existingFeedback.remove();
            }

            // 显示进度
            const progressDiv = document.createElement('div');
            progressDiv.style.cssText = `
                background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                color: #1976d2;
                padding: 10px 15px;
                border-radius: 20px;
                margin-bottom: 20px;
                text-align: center;
                font-weight: bold;
                border: 2px solid #64b5f6;
            `;
            progressDiv.textContent = `进度：${practicedWords.length}/${currentWords.length} (剩余 ${currentWords.length - practicedWords.length} 个)`;
            studyArea.appendChild(progressDiv);

            const questionDiv = document.createElement('div');
            questionDiv.className = 'question';
            questionDiv.textContent = currentQuestion.chinese;
            studyArea.appendChild(questionDiv);

            // 添加读音按钮
            const speakDiv = document.createElement('div');
            speakDiv.style.textAlign = 'center';
            speakDiv.style.marginBottom = '20px';
            const speakBtn = document.createElement('button');
            speakBtn.className = 'speak-btn';
            speakBtn.innerHTML = '🔊 听读音';
            speakBtn.onclick = () => speakWord(currentQuestion.english);
            speakDiv.appendChild(speakBtn);
            studyArea.appendChild(speakDiv);

            switch (currentMode) {
                case 'default':
                    displayDefaultMode(studyArea);
                    break;
                case 'choice':
                    displayChoiceMode(studyArea);
                    break;
                case 'typing':
                    displayTypingMode(studyArea);
                    break;
            }
        }

        function displayDefaultMode(container) {
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'answer-input';
            input.placeholder = '请输入英文答案';
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    checkDefaultAnswer(this.value);
                }
            });
            container.appendChild(input);

            const submitBtn = document.createElement('button');
            submitBtn.textContent = '提交答案';
            submitBtn.onclick = () => checkDefaultAnswer(input.value);
            container.appendChild(submitBtn);

            input.focus();
        }

        function displayChoiceMode(container) {
            const choices = generateChoices(currentQuestion.english);
            const choicesDiv = document.createElement('div');
            choicesDiv.className = 'choices';

            choices.forEach((choice, index) => {
                const button = document.createElement('button');
                button.className = 'choice-button';
                button.textContent = choice;
                button.onclick = () => checkChoiceAnswer(choice);
                choicesDiv.appendChild(button);
            });

            container.appendChild(choicesDiv);
        }

        function displayTypingMode(container) {
            const word = currentQuestion.english.toLowerCase();
            const typingDiv = document.createElement('div');
            typingDiv.className = 'typing-area';

            for (let i = 0; i < word.length; i++) {
                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'letter-input';
                input.maxLength = 1;
                input.dataset.index = i;
                input.addEventListener('input', handleTypingInput);
                input.addEventListener('keydown', handleTypingKeydown);
                typingDiv.appendChild(input);
            }

            container.appendChild(typingDiv);

            const checkBtn = document.createElement('button');
            checkBtn.textContent = '检查答案';
            checkBtn.onclick = checkTypingAnswer;
            container.appendChild(checkBtn);

            // 聚焦第一个输入框
            typingDiv.firstChild.focus();
        }

        function generateChoices(correctAnswer) {
            const choices = [correctAnswer];

            // 生成3个干扰项
            const allWords = currentWords.map(w => w.english).filter(w => w !== correctAnswer);
            while (choices.length < 4 && allWords.length > 0) {
                const randomIndex = Math.floor(Math.random() * allWords.length);
                const randomWord = allWords.splice(randomIndex, 1)[0];
                choices.push(randomWord);
            }

            // 如果词表中的单词不够，生成一些简单的干扰项
            while (choices.length < 4) {
                choices.push(`option${choices.length}`);
            }

            // 打乱顺序
            return choices.sort(() => Math.random() - 0.5);
        }

        function handleTypingInput(e) {
            const input = e.target;
            const index = parseInt(input.dataset.index);
            const typingArea = input.parentElement;
            const inputs = typingArea.querySelectorAll('.letter-input');

            if (input.value && index < inputs.length - 1) {
                inputs[index + 1].focus();
            }
        }

        function handleTypingKeydown(e) {
            const input = e.target;
            const index = parseInt(input.dataset.index);
            const typingArea = input.parentElement;
            const inputs = typingArea.querySelectorAll('.letter-input');

            if (e.key === 'Backspace' && !input.value && index > 0) {
                inputs[index - 1].focus();
                inputs[index - 1].value = '';
            }
        }

        // 答案检查功能
        function checkDefaultAnswer(userAnswer) {
            const isCorrect = userAnswer.toLowerCase().trim() === currentQuestion.english.toLowerCase();
            showFeedback(isCorrect, currentQuestion.english);
            updateStats(isCorrect);

            if (!isCorrect) {
                addToWrongWords(currentQuestion);
            }

            setTimeout(() => {
                if (isStudying) {
                    nextQuestion();
                }
            }, 2000);
        }

        function checkChoiceAnswer(selectedAnswer) {
            const isCorrect = selectedAnswer.toLowerCase() === currentQuestion.english.toLowerCase();
            showFeedback(isCorrect, currentQuestion.english);
            updateStats(isCorrect);

            if (!isCorrect) {
                addToWrongWords(currentQuestion);
            }

            // 禁用所有选择按钮
            document.querySelectorAll('.choice-button').forEach(btn => {
                btn.disabled = true;
                if (btn.textContent.toLowerCase() === currentQuestion.english.toLowerCase()) {
                    btn.style.background = 'linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%)';
                    btn.style.color = '#2e7d32';
                }
            });

            setTimeout(() => {
                if (isStudying) {
                    nextQuestion();
                }
            }, 2000);
        }

        function checkTypingAnswer() {
            const inputs = document.querySelectorAll('.letter-input');
            const userAnswer = Array.from(inputs).map(input => input.value).join('').toLowerCase();
            const correctAnswer = currentQuestion.english.toLowerCase();

            const isCorrect = userAnswer === correctAnswer;
            showFeedback(isCorrect, currentQuestion.english);
            updateStats(isCorrect);

            if (!isCorrect) {
                addToWrongWords(currentQuestion);
            }

            setTimeout(() => {
                if (isStudying) {
                    nextQuestion();
                }
            }, 2000);
        }

        // 反馈和统计功能
        function showFeedback(isCorrect, correctAnswer) {
            const studyArea = document.getElementById('studyArea');
            const feedback = document.createElement('div');
            feedback.className = `feedback ${isCorrect ? 'correct' : 'incorrect'}`;

            if (isCorrect) {
                feedback.innerHTML = '✅ 回答正确！';
            } else {
                feedback.innerHTML = `❌ 回答错误！正确答案是：${correctAnswer}`;

                // 添加读音按钮
                const speakBtn = document.createElement('button');
                speakBtn.className = 'speak-btn';
                speakBtn.innerHTML = '🔊 听读音';
                speakBtn.onclick = () => speakWord(correctAnswer);
                speakBtn.style.marginLeft = '10px';
                feedback.appendChild(speakBtn);
            }

            studyArea.appendChild(feedback);
        }

        function updateStats(isCorrect) {
            stats.total++;
            if (isCorrect) {
                stats.correct++;
            } else {
                stats.wrong++;
            }

            document.getElementById('totalCount').textContent = stats.total;
            document.getElementById('correctCount').textContent = stats.correct;
            document.getElementById('wrongCount').textContent = stats.wrong;

            const accuracy = stats.total > 0 ? Math.round((stats.correct / stats.total) * 100) : 0;
            document.getElementById('accuracy').textContent = accuracy + '%';
        }

        function resetStats() {
            stats = { total: 0, correct: 0, wrong: 0 };
            isStudying = false;

            // 重新显示词汇管理区域
            showWordManagement();

            // 切换按钮显示状态
            document.getElementById('startBtn').style.display = 'inline-block';
            document.getElementById('stopBtn').style.display = 'none';

            // 直接更新显示
            document.getElementById('totalCount').textContent = '0';
            document.getElementById('correctCount').textContent = '0';
            document.getElementById('wrongCount').textContent = '0';
            document.getElementById('accuracy').textContent = '0%';

            document.getElementById('studyArea').innerHTML = '<p>请先加载词表，然后选择学习模式开始学习</p>';
            alert('统计数据已重置！');
        }

        // 错词本功能
        function addToWrongWords(word) {
            const existingIndex = wrongWords.findIndex(w => w.chinese === word.chinese && w.english === word.english);

            if (existingIndex >= 0) {
                wrongWords[existingIndex].count++;
            } else {
                wrongWords.push({
                    chinese: word.chinese,
                    english: word.english,
                    count: 1
                });
            }

            localStorage.setItem('wrongWords', JSON.stringify(wrongWords));
            updateWrongWordsList();
        }

        function updateWrongWordsList() {
            const container = document.getElementById('wrongWordsList');

            if (wrongWords.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666;">暂无错词</p>';
                return;
            }

            container.innerHTML = '';
            wrongWords.forEach((word, index) => {
                const wordItem = document.createElement('div');
                wordItem.className = 'word-item';

                wordItem.innerHTML = `
                    <div class="word-content">
                        <span class="word-chinese">${word.chinese}</span>
                        <span class="word-english" id="english-${index}">***</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 5px;">
                        <span class="error-count">错误${word.count}次</span>
                        <button class="speak-btn" id="speak-btn-${index}">🔊</button>
                        <button class="show-btn" id="show-btn-${index}" onclick="toggleEnglishDisplay(${index})">显示</button>
                        <button class="delete-btn" onclick="removeWrongWord(${index})">删除</button>
                    </div>
                `;

                // 存储英文单词到按钮的data属性中，避免引号问题
                const showBtn = wordItem.querySelector(`#show-btn-${index}`);
                const speakBtn = wordItem.querySelector(`#speak-btn-${index}`);
                showBtn.dataset.english = word.english;
                speakBtn.dataset.english = word.english;
                speakBtn.onclick = () => speakWord(word.english);

                container.appendChild(wordItem);
            });
        }

        function toggleEnglishDisplay(index) {
            const englishSpan = document.getElementById(`english-${index}`);
            const showBtn = document.getElementById(`show-btn-${index}`);
            const englishWord = showBtn.dataset.english;

            if (englishSpan.textContent === '***') {
                // 显示英文单词
                englishSpan.textContent = englishWord;
                showBtn.textContent = '隐藏';
                showBtn.style.background = '#ff9800';
            } else {
                // 隐藏英文单词
                englishSpan.textContent = '***';
                showBtn.textContent = '显示';
                showBtn.style.background = '#4caf50';
            }
        }

        function toggleAllEnglish() {
            if (wrongWords.length === 0) {
                alert('错词本为空！');
                return;
            }

            // 检查第一个单词的显示状态来决定全部显示还是隐藏
            const firstEnglishSpan = document.getElementById('english-0');
            if (!firstEnglishSpan) return;

            const shouldShow = firstEnglishSpan.textContent === '***';

            wrongWords.forEach((word, index) => {
                const englishSpan = document.getElementById(`english-${index}`);
                const showBtn = document.getElementById(`show-btn-${index}`);

                if (shouldShow) {
                    // 全部显示
                    englishSpan.textContent = word.english;
                    showBtn.textContent = '隐藏';
                    showBtn.style.background = '#ff9800';
                } else {
                    // 全部隐藏
                    englishSpan.textContent = '***';
                    showBtn.textContent = '显示';
                    showBtn.style.background = '#4caf50';
                }
            });
        }

        function removeWrongWord(index) {
            wrongWords.splice(index, 1);
            localStorage.setItem('wrongWords', JSON.stringify(wrongWords));
            updateWrongWordsList();
        }

        function clearWrongWords() {
            if (wrongWords.length === 0) {
                alert('错词本已经是空的！');
                return;
            }

            if (confirm('确定要清空错词本吗？')) {
                wrongWords = [];
                localStorage.setItem('wrongWords', JSON.stringify(wrongWords));
                updateWrongWordsList();
                alert('错词本已清空！');
            }
        }

        function practiceWrongWords() {
            if (wrongWords.length === 0) {
                alert('错词本为空，无法练习！');
                return;
            }

            // 将错词转换为练习格式
            currentWords = wrongWords.map(w => ({ chinese: w.chinese, english: w.english }));
            updateWordCount();

            // 开始学习
            isStudying = true;
            currentIndex = 0;
            practicedWords = []; // 重置已练习单词列表

            // 隐藏词汇管理区域
            hideWordManagement();

            // 切换按钮显示状态
            document.getElementById('startBtn').style.display = 'none';
            document.getElementById('stopBtn').style.display = 'inline-block';

            nextQuestion();

            alert(`开始练习错词本中的 ${wrongWords.length} 个单词！`);
        }

        function exportWrongWords() {
            if (wrongWords.length === 0) {
                alert('错词本为空！');
                return;
            }

            const dataStr = JSON.stringify(wrongWords, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `错词本_${new Date().toISOString().slice(0, 10)}.json`;
            link.click();

            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
