<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>背单词小程序</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif;
            background: linear-gradient(135deg, #e0f7fa 0%, #e3f2fd 100%);
            margin: 0; padding: 0;
        }
        .container {
            max-width: 480px;
            margin: 40px auto;
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 4px 32px 0 rgba(33,150,243,0.10), 0 1.5px 6px 0 rgba(0,0,0,0.04);
            padding: 36px 28px 32px 28px;
        }
        h2 {
            text-align: center;
            font-size: 2.1em;
            letter-spacing: 2px;
            color: #1976d2;
            margin-bottom: 18px;
        }
        textarea, input[type="text"] {
            width: 100%;
            padding: 12px 14px;
            font-size: 1.1em;
            border: 1.5px solid #b3e5fc;
            border-radius: 10px;
            margin-bottom: 14px;
            outline: none;
            transition: border 0.2s;
            background: #f7fbfd;
        }
        textarea:focus, input[type="text"]:focus {
            border: 1.5px solid #29b6f6;
            background: #e1f5fe;
        }
        button {
            padding: 12px 22px;
            margin: 8px 6px 8px 0;
            border: none;
            border-radius: 8px;
            background: linear-gradient(90deg, #4dd0e1 0%, #42a5f5 100%);
            color: #fff;
            font-size: 1.08em;
            font-weight: 500;
            cursor: pointer;
            box-shadow: 0 2px 8px 0 rgba(33,150,243,0.08);
            transition: background 0.2s, box-shadow 0.2s;
        }
        button:hover, button:focus {
            background: linear-gradient(90deg, #42a5f5 0%, #4dd0e1 100%);
            box-shadow: 0 4px 16px 0 rgba(33,150,243,0.13);
        }
        .question {
            font-size: 1.25em;
            margin: 24px 0 16px 0;
            text-align: center;
            color: #1976d2;
            font-weight: 500;
        }
        .result {
            font-weight: bold;
            margin: 12px 0 0 0;
            text-align: center;
            font-size: 1.1em;
        }
        .score {
            margin: 12px 0 0 0;
            text-align: center;
            color: #009688;
            font-size: 1.05em;
        }
        #choice-options button, #typing-inputs input {
            margin: 0 6px 0 0;
        }
        #choice-options button {
            background: #e3f2fd;
            color: #1976d2;
            border: 1.5px solid #90caf9;
            border-radius: 7px;
            font-size: 1.08em;
            font-weight: 500;
            transition: background 0.2s, color 0.2s;
        }
        #choice-options button:hover, #choice-options button:focus {
            background: #bbdefb;
            color: #0d47a1;
        }
        #typing-inputs input {
            width: 38px;
            height: 38px;
            font-size: 1.2em;
            border-radius: 7px;
            border: 1.5px solid #b3e5fc;
            background: #f7fbfd;
            text-transform: lowercase;
        }
        #typing-inputs input:focus {
            border: 1.5px solid #29b6f6;
            background: #e1f5fe;
        }
        #wrongwords-panel {
            background: #f7fbfd;
            border-radius: 12px;
            box-shadow: 0 2px 8px 0 rgba(33,150,243,0.07);
            padding: 18px 12px 12px 12px;
            max-width: 420px;
            margin: 24px auto;
        }
        #wrongwords-panel table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }
        #wrongwords-panel th, #wrongwords-panel td {
            padding: 8px 4px;
            text-align: center;
        }
        #wrongwords-panel th {
            color: #1976d2;
            font-weight: 600;
            border-bottom: 2px solid #b3e5fc;
        }
        #wrongwords-panel td {
            border-bottom: 1px solid #e0e0e0;
        }
        #wrongwords-panel button {
            background: #ff8a65;
            color: #fff;
            border-radius: 6px;
            padding: 6px 14px;
            font-size: 0.98em;
            margin: 0 2px;
        }
        #wrongwords-panel button:hover {
            background: #ff7043;
        }
        @media (max-width: 600px) {
            .container { max-width: 98vw; padding: 10vw 2vw; }
            #wrongwords-panel { max-width: 98vw; }
            button, input, textarea { font-size: 1em; }
        }
    </style>
    <!-- 其余内容请见下方脚本和结构 -->
</head>
<body>
<div class="container">
    <h2>背单词小程序</h2>
    <!-- 单词输入与词表管理区 -->
    <div id="wordlist-manager" style="margin-bottom:18px;">
        <label style="font-weight:500;">词表名称：</label>
        <input type="text" id="wordlist-name" style="width:120px;">
        <button onclick="saveWordlist()">保存词表</button>
        <button onclick="newWordlist()">新建词表</button>
        <select id="wordlist-select" onchange="loadWordlist()" style="margin-left:8px;"></select>
        <button onclick="deleteWordlist()">删除词表</button>
        <button onclick="exportWordlists()">导出词表</button>
        <input type="file" id="import-file" style="display:none" accept="application/json" onchange="importWordlists(event)">
        <button onclick="document.getElementById('import-file').click()">导入词表</button>
    </div>
    <div id="wordlist-input" style="margin-bottom:18px;">
        <p style="margin-bottom:6px;">请输入中英文对照单词列表（每行一个，格式：中文 英文 或 中文,英文）：</p>
        <textarea id="wordlist" placeholder="例如：\n苹果 apple\n香蕉 banana\n...\n"></textarea>
    </div>
    <!-- 功能按钮区 -->
    <div style="margin-bottom:18px;">
        <button onclick="startQuiz()">开始背单词</button>
        <button onclick="startChoiceQuiz()">开始选择题</button>
        <button onclick="startTypingQuiz()">开始打字模式</button>
        <button onclick="showWrongWordsPanel()">管理错词本</button>
    </div>
    <!-- 背单词模式 -->
    <div id="quiz" style="display:none;">
        <div class="question" id="question"></div>
        <input type="text" id="answer" placeholder="请输入英文单词" onkeydown="if(event.key==='Enter'){checkAnswer();}">
        <button onclick="checkAnswer()">提交</button>
        <div class="result" id="result"></div>
        <div class="score" id="score"></div>
        <button onclick="nextWord()" id="nextBtn" style="display:none;">下一个</button>
        <button onclick="restart()">重新开始</button>
    </div>
    <!-- 选择题模式 -->
    <div id="choice-quiz" style="display:none;">
        <div class="question" id="choice-question"></div>
        <div id="choice-options" style="margin: 20px 0; display: flex; flex-direction: column; gap: 12px;"></div>
        <div class="result" id="choice-result"></div>
        <div class="score" id="choice-score"></div>
        <button onclick="nextChoice()" id="choiceNextBtn" style="display:none;">下一个</button>
        <button onclick="restart()">重新开始</button>
    </div>
    <!-- 打字模式 -->
    <div id="typing-quiz" style="display:none;">
        <div class="question" id="typing-question"></div>
        <div id="typing-inputs" style="display:flex; gap:6px; margin:20px 0;"></div>
        <div class="result" id="typing-result"></div>
        <div class="score" id="typing-score"></div>
        <button onclick="nextTyping()" id="typingNextBtn" style="display:none;">下一个</button>
        <button onclick="restart()">重新开始</button>
    </div>
    <!-- 错词本管理区 -->
    <div id="wrongwords-panel" style="display:none; margin:20px 0;"></div>
</div>
<script>
function startQuiz() {
    // ...背单词模式的逻辑...
}
function startChoiceQuiz() {
    // ...选择题模式的逻辑...
}
// 其余所有功能函数...
</script>
</body>
</html> 