/**
 * IndexedDB 数据库操作模块
 * 用于管理词表、学习记录和错题数据
 */

class WordDatabase {
    constructor() {
        this.dbName = 'WordLearnerDB';
        this.version = 1;
        this.db = null;
    }

    /**
     * 初始化数据库
     */
    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                console.error('数据库打开失败:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('数据库初始化成功');
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // 词表存储
                if (!db.objectStoreNames.contains('wordlists')) {
                    const wordlistStore = db.createObjectStore('wordlists', { keyPath: 'id', autoIncrement: true });
                    wordlistStore.createIndex('name', 'name', { unique: true });
                    wordlistStore.createIndex('createdAt', 'createdAt', { unique: false });
                }

                // 学习记录存储
                if (!db.objectStoreNames.contains('studyRecords')) {
                    const recordStore = db.createObjectStore('studyRecords', { keyPath: 'id', autoIncrement: true });
                    recordStore.createIndex('wordlistId', 'wordlistId', { unique: false });
                    recordStore.createIndex('date', 'date', { unique: false });
                }

                // 错题存储
                if (!db.objectStoreNames.contains('wrongWords')) {
                    const wrongStore = db.createObjectStore('wrongWords', { keyPath: 'id', autoIncrement: true });
                    wrongStore.createIndex('wordlistId', 'wordlistId', { unique: false });
                    wrongStore.createIndex('chinese', 'chinese', { unique: false });
                    wrongStore.createIndex('wrongCount', 'wrongCount', { unique: false });
                }

                console.log('数据库结构创建完成');
            };
        });
    }

    /**
     * 保存词表
     */
    async saveWordlist(name, words) {
        const transaction = this.db.transaction(['wordlists'], 'readwrite');
        const store = transaction.objectStore('wordlists');
        
        const wordlist = {
            name: name,
            words: words,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            totalWords: words.length
        };

        return new Promise((resolve, reject) => {
            const request = store.put(wordlist);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 获取所有词表
     */
    async getAllWordlists() {
        const transaction = this.db.transaction(['wordlists'], 'readonly');
        const store = transaction.objectStore('wordlists');
        
        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 根据名称获取词表
     */
    async getWordlistByName(name) {
        const transaction = this.db.transaction(['wordlists'], 'readonly');
        const store = transaction.objectStore('wordlists');
        const index = store.index('name');
        
        return new Promise((resolve, reject) => {
            const request = index.get(name);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 删除词表
     */
    async deleteWordlist(name) {
        const wordlist = await this.getWordlistByName(name);
        if (!wordlist) return false;

        const transaction = this.db.transaction(['wordlists'], 'readwrite');
        const store = transaction.objectStore('wordlists');
        
        return new Promise((resolve, reject) => {
            const request = store.delete(wordlist.id);
            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 保存学习记录
     */
    async saveStudyRecord(wordlistId, mode, correct, wrong, totalTime) {
        const transaction = this.db.transaction(['studyRecords'], 'readwrite');
        const store = transaction.objectStore('studyRecords');
        
        const record = {
            wordlistId: wordlistId,
            mode: mode,
            correct: correct,
            wrong: wrong,
            total: correct + wrong,
            accuracy: correct / (correct + wrong) * 100,
            totalTime: totalTime,
            date: new Date().toISOString()
        };

        return new Promise((resolve, reject) => {
            const request = store.add(record);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 获取学习记录
     */
    async getStudyRecords(wordlistId = null, limit = 50) {
        const transaction = this.db.transaction(['studyRecords'], 'readonly');
        const store = transaction.objectStore('studyRecords');
        
        return new Promise((resolve, reject) => {
            let request;
            if (wordlistId) {
                const index = store.index('wordlistId');
                request = index.getAll(wordlistId);
            } else {
                request = store.getAll();
            }
            
            request.onsuccess = () => {
                const records = request.result
                    .sort((a, b) => new Date(b.date) - new Date(a.date))
                    .slice(0, limit);
                resolve(records);
            };
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 添加错题
     */
    async addWrongWord(wordlistId, chinese, english) {
        const transaction = this.db.transaction(['wrongWords'], 'readwrite');
        const store = transaction.objectStore('wrongWords');
        
        // 检查是否已存在
        const index = store.index('chinese');
        const existing = await new Promise((resolve) => {
            const request = index.get(chinese);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => resolve(null);
        });

        if (existing) {
            // 更新错误次数
            existing.wrongCount += 1;
            existing.lastWrongDate = new Date().toISOString();
            return new Promise((resolve, reject) => {
                const updateRequest = store.put(existing);
                updateRequest.onsuccess = () => resolve(updateRequest.result);
                updateRequest.onerror = () => reject(updateRequest.error);
            });
        } else {
            // 添加新错题
            const wrongWord = {
                wordlistId: wordlistId,
                chinese: chinese,
                english: english,
                wrongCount: 1,
                firstWrongDate: new Date().toISOString(),
                lastWrongDate: new Date().toISOString()
            };
            
            return new Promise((resolve, reject) => {
                const request = store.add(wrongWord);
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        }
    }

    /**
     * 获取错题列表
     */
    async getWrongWords(wordlistId = null) {
        const transaction = this.db.transaction(['wrongWords'], 'readonly');
        const store = transaction.objectStore('wrongWords');
        
        return new Promise((resolve, reject) => {
            let request;
            if (wordlistId) {
                const index = store.index('wordlistId');
                request = index.getAll(wordlistId);
            } else {
                request = store.getAll();
            }
            
            request.onsuccess = () => {
                const wrongWords = request.result
                    .sort((a, b) => b.wrongCount - a.wrongCount);
                resolve(wrongWords);
            };
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 删除错题
     */
    async deleteWrongWord(id) {
        const transaction = this.db.transaction(['wrongWords'], 'readwrite');
        const store = transaction.objectStore('wrongWords');
        
        return new Promise((resolve, reject) => {
            const request = store.delete(id);
            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 清空所有错题
     */
    async clearAllWrongWords() {
        const transaction = this.db.transaction(['wrongWords'], 'readwrite');
        const store = transaction.objectStore('wrongWords');
        
        return new Promise((resolve, reject) => {
            const request = store.clear();
            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * 导出所有数据
     */
    async exportAllData() {
        const [wordlists, studyRecords, wrongWords] = await Promise.all([
            this.getAllWordlists(),
            this.getStudyRecords(),
            this.getWrongWords()
        ]);

        return {
            wordlists,
            studyRecords,
            wrongWords,
            exportDate: new Date().toISOString(),
            version: this.version
        };
    }

    /**
     * 导入数据
     */
    async importData(data) {
        const transaction = this.db.transaction(['wordlists', 'studyRecords', 'wrongWords'], 'readwrite');
        
        try {
            // 导入词表
            if (data.wordlists) {
                const wordlistStore = transaction.objectStore('wordlists');
                for (const wordlist of data.wordlists) {
                    delete wordlist.id; // 删除原ID，让数据库自动生成
                    await new Promise((resolve, reject) => {
                        const request = wordlistStore.add(wordlist);
                        request.onsuccess = () => resolve();
                        request.onerror = () => reject(request.error);
                    });
                }
            }

            return true;
        } catch (error) {
            console.error('数据导入失败:', error);
            return false;
        }
    }
}

// 创建全局数据库实例
window.wordDB = new WordDatabase();
