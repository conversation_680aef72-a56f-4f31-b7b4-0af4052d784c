/**
 * 学习模式模块
 * 处理拼写模式、选择题模式和错题复习功能
 */

class QuizManager {
    constructor() {
        this.currentMode = null;
        this.words = [];
        this.currentWordIndex = 0;
        this.currentWord = null;
        this.correctCount = 0;
        this.wrongCount = 0;
        this.wrongWords = [];
        this.startTime = null;
        this.usedIndexes = [];
        this.countdownTimer = null;

        this.initEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 模式选择按钮
        document.getElementById('spellingModeBtn').addEventListener('click', () => this.startSpellingMode());
        document.getElementById('choiceModeBtn').addEventListener('click', () => this.startChoiceMode());
        document.getElementById('reviewModeBtn').addEventListener('click', () => this.startReviewMode());
        
        // 学习界面按钮
        document.getElementById('submitBtn').addEventListener('click', () => this.submitAnswer());
        document.getElementById('nextBtn').addEventListener('click', () => this.nextQuestion());
        document.getElementById('backToMenuBtn').addEventListener('click', () => this.backToMenu());
        
        // 完成界面按钮
        document.getElementById('restartBtn').addEventListener('click', () => this.restartQuiz());
        document.getElementById('reviewErrorsBtn').addEventListener('click', () => this.startReviewMode());
        document.getElementById('backHomeBtn').addEventListener('click', () => this.backToMenu());
        
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
    }

    /**
     * 开始拼写模式
     */
    async startSpellingMode() {
        if (!this.validateWordlist()) return;
        
        this.currentMode = 'spelling';
        this.initQuiz();
        this.showQuizContainer();
        this.nextQuestion();
    }

    /**
     * 开始选择题模式
     */
    async startChoiceMode() {
        if (!this.validateWordlist()) return;
        
        if (this.words.length < 4) {
            wordlistManager.showMessage('选择题模式至少需要4个单词', 'error');
            return;
        }
        
        this.currentMode = 'choice';
        this.initQuiz();
        this.showQuizContainer();
        this.nextQuestion();
    }

    /**
     * 开始错题复习模式
     */
    async startReviewMode() {
        try {
            const wrongWords = await wordDB.getWrongWords();
            if (wrongWords.length === 0) {
                wordlistManager.showMessage('暂无错题需要复习', 'info');
                return;
            }
            
            this.words = wrongWords.map(item => ({
                chinese: item.chinese,
                english: item.english
            }));
            
            this.currentMode = 'review';
            this.initQuiz();
            this.showQuizContainer();
            this.nextQuestion();
        } catch (error) {
            console.error('加载错题失败:', error);
            wordlistManager.showMessage('加载错题失败', 'error');
        }
    }

    /**
     * 验证词表
     */
    validateWordlist() {
        if (!wordlistManager.hasWords()) {
            wordlistManager.showMessage('请先输入单词内容', 'error');
            return false;
        }
        return true;
    }

    /**
     * 初始化测验
     */
    initQuiz() {
        if (this.currentMode !== 'review') {
            this.words = wordlistManager.getCurrentWords();
        }
        
        this.currentWordIndex = 0;
        this.correctCount = 0;
        this.wrongCount = 0;
        this.wrongWords = [];
        this.startTime = Date.now();
        this.usedIndexes = [];
        
        // 打乱单词顺序
        this.shuffleArray(this.words);
        
        this.updateStats();
        this.updateProgress();
    }

    /**
     * 显示测验容器
     */
    showQuizContainer() {
        document.getElementById('modeSelection').classList.add('hidden');
        document.getElementById('wordlistManager').classList.add('hidden');
        document.getElementById('quizContainer').classList.remove('hidden');
        document.getElementById('completionScreen').classList.add('hidden');
    }

    /**
     * 下一题
     */
    nextQuestion() {
        // 清除可能存在的倒计时
        this.clearAutoNextCountdown();

        if (this.usedIndexes.length >= this.words.length) {
            this.completeQuiz();
            return;
        }

        // 随机选择未使用的单词
        let wordIndex;
        do {
            wordIndex = Math.floor(Math.random() * this.words.length);
        } while (this.usedIndexes.includes(wordIndex));

        this.usedIndexes.push(wordIndex);
        this.currentWord = this.words[wordIndex];
        this.currentWordIndex = this.usedIndexes.length;

        this.updateProgress();
        this.clearResult();

        if (this.currentMode === 'spelling' || this.currentMode === 'review') {
            this.showSpellingQuestion();
        } else if (this.currentMode === 'choice') {
            this.showChoiceQuestion();
        }
    }

    /**
     * 显示拼写题目
     */
    showSpellingQuestion() {
        document.getElementById('questionText').textContent = `请输入"${this.currentWord.chinese}"的英文：`;
        
        const answerArea = document.getElementById('answerArea');
        answerArea.innerHTML = `
            <input type="text" id="spellingInput" 
                   class="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none text-center"
                   placeholder="输入英文单词" autocomplete="off">
        `;
        
        document.getElementById('submitBtn').classList.remove('hidden');
        document.getElementById('nextBtn').classList.add('hidden');
        
        // 聚焦输入框
        setTimeout(() => {
            document.getElementById('spellingInput').focus();
        }, 100);
    }

    /**
     * 显示选择题目
     */
    showChoiceQuestion() {
        document.getElementById('questionText').textContent = `"${this.currentWord.chinese}"的英文是：`;
        
        // 生成选项
        const options = [this.currentWord.english];
        while (options.length < 4) {
            const randomWord = this.words[Math.floor(Math.random() * this.words.length)];
            if (!options.includes(randomWord.english)) {
                options.push(randomWord.english);
            }
        }
        
        this.shuffleArray(options);
        
        const answerArea = document.getElementById('answerArea');
        answerArea.innerHTML = options.map((option, index) => `
            <button class="choice-option w-full p-4 mb-3 text-left border-2 border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
                    data-answer="${option}">
                ${String.fromCharCode(65 + index)}. ${option}
            </button>
        `).join('');
        
        // 添加选项点击事件
        document.querySelectorAll('.choice-option').forEach(btn => {
            btn.addEventListener('click', (e) => this.selectChoice(e.target));
        });
        
        document.getElementById('submitBtn').classList.add('hidden');
        document.getElementById('nextBtn').classList.add('hidden');
    }

    /**
     * 选择选项
     */
    selectChoice(button) {
        // 防止重复点击
        if (button.disabled) return;

        // 禁用所有选项
        document.querySelectorAll('.choice-option').forEach(btn => {
            btn.disabled = true;
        });

        // 移除其他选项的选中状态
        document.querySelectorAll('.choice-option').forEach(btn => {
            btn.classList.remove('bg-blue-500', 'text-white', 'border-blue-500');
            btn.classList.add('border-gray-300');
        });

        // 标记当前选项为选中
        button.classList.add('bg-blue-500', 'text-white', 'border-blue-500');
        button.classList.remove('border-gray-300');

        // 立即提交答案（不需要额外延迟，因为submitAnswer中已有0.5s延迟）
        this.submitAnswer(button.dataset.answer);
    }

    /**
     * 提交答案
     */
    async submitAnswer(choiceAnswer = null) {
        let userAnswer;

        if (this.currentMode === 'choice') {
            userAnswer = choiceAnswer;
        } else {
            const input = document.getElementById('spellingInput');
            if (!input) return;
            userAnswer = input.value.trim().toLowerCase();
        }

        if (!userAnswer) {
            wordlistManager.showMessage('请输入答案', 'error');
            return;
        }

        const correctAnswer = this.currentWord.english.toLowerCase();
        const isCorrect = userAnswer === correctAnswer;

        if (isCorrect) {
            this.correctCount++;
            this.showResult('正确！', 'success');
        } else {
            this.wrongCount++;
            this.wrongWords.push(this.currentWord);

            // 添加到错题本
            try {
                await wordDB.addWrongWord(1, this.currentWord.chinese, this.currentWord.english);
            } catch (error) {
                console.error('添加错题失败:', error);
            }

            this.showResult(`错误！正确答案是：${this.currentWord.english}`, 'error');
        }

        this.updateStats();

        // 禁用输入
        if (this.currentMode !== 'choice') {
            document.getElementById('spellingInput').disabled = true;
        }

        document.getElementById('submitBtn').classList.add('hidden');
        document.getElementById('nextBtn').classList.remove('hidden');

        // 显示倒计时进度条并在0.5秒后自动切换到下一题
        this.showAutoNextCountdown();
    }

    /**
     * 显示结果
     */
    showResult(message, type) {
        const resultArea = document.getElementById('resultArea');
        resultArea.innerHTML = `
            <div class="p-4 rounded-lg ${type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-times-circle'} mr-2"></i>
                ${message}
                <div class="mt-3">
                    <div class="flex items-center justify-between text-sm opacity-75 mb-1">
                        <span><i class="fas fa-clock mr-1"></i>自动切换到下一题</span>
                        <span id="countdownText">0.5s</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-1 cursor-pointer" onclick="quizManager.nextQuestion()" title="点击立即切换">
                        <div id="countdownBar" class="bg-current h-1 rounded-full transition-all duration-100" style="width: 100%"></div>
                    </div>
                    <div class="text-xs text-center mt-1 opacity-60">点击进度条可立即切换</div>
                </div>
            </div>
        `;
    }

    /**
     * 显示自动切换倒计时
     */
    showAutoNextCountdown() {
        // 清除之前的倒计时
        this.clearAutoNextCountdown();

        const countdownDuration = 500; // 0.5秒
        const updateInterval = 50; // 每50ms更新一次
        let remainingTime = countdownDuration;

        const countdownText = document.getElementById('countdownText');
        const countdownBar = document.getElementById('countdownBar');

        const updateCountdown = () => {
            remainingTime -= updateInterval;
            const progress = (remainingTime / countdownDuration) * 100;
            const seconds = (remainingTime / 1000).toFixed(1);

            if (countdownText) {
                countdownText.textContent = `${seconds}s`;
            }
            if (countdownBar) {
                countdownBar.style.width = `${Math.max(0, progress)}%`;
            }

            if (remainingTime <= 0) {
                this.countdownTimer = null;
                this.nextQuestion();
            } else {
                this.countdownTimer = setTimeout(updateCountdown, updateInterval);
            }
        };

        this.countdownTimer = setTimeout(updateCountdown, updateInterval);
    }

    /**
     * 清除自动切换倒计时
     */
    clearAutoNextCountdown() {
        if (this.countdownTimer) {
            clearTimeout(this.countdownTimer);
            this.countdownTimer = null;
        }
    }

    /**
     * 清除结果显示
     */
    clearResult() {
        document.getElementById('resultArea').innerHTML = '';
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        document.getElementById('correctCount').textContent = this.correctCount;
        document.getElementById('wrongCount').textContent = this.wrongCount;
        
        const total = this.correctCount + this.wrongCount;
        const accuracy = total > 0 ? Math.round((this.correctCount / total) * 100) : 0;
        document.getElementById('accuracyRate').textContent = `${accuracy}%`;
    }

    /**
     * 更新进度
     */
    updateProgress() {
        const total = this.words.length;
        const current = this.usedIndexes.length;
        const progress = total > 0 ? (current / total) * 100 : 0;
        
        document.getElementById('progressText').textContent = `${current}/${total}`;
        document.getElementById('progressBar').style.width = `${progress}%`;
    }

    /**
     * 完成测验
     */
    async completeQuiz() {
        const totalTime = Date.now() - this.startTime;
        
        // 保存学习记录
        try {
            await wordDB.saveStudyRecord(1, this.currentMode, this.correctCount, this.wrongCount, totalTime);
        } catch (error) {
            console.error('保存学习记录失败:', error);
        }
        
        // 显示完成界面
        this.showCompletionScreen();
    }

    /**
     * 显示完成界面
     */
    showCompletionScreen() {
        document.getElementById('quizContainer').classList.add('hidden');
        document.getElementById('completionScreen').classList.remove('hidden');
        
        const total = this.correctCount + this.wrongCount;
        const accuracy = total > 0 ? Math.round((this.correctCount / total) * 100) : 0;
        
        document.getElementById('finalCorrect').textContent = this.correctCount;
        document.getElementById('finalWrong').textContent = this.wrongCount;
        document.getElementById('finalAccuracy').textContent = `${accuracy}%`;
        
        // 如果没有错题，隐藏复习错题按钮
        if (this.wrongWords.length === 0) {
            document.getElementById('reviewErrorsBtn').classList.add('hidden');
        } else {
            document.getElementById('reviewErrorsBtn').classList.remove('hidden');
        }
    }

    /**
     * 重新开始测验
     */
    restartQuiz() {
        if (this.currentMode === 'spelling') {
            this.startSpellingMode();
        } else if (this.currentMode === 'choice') {
            this.startChoiceMode();
        } else if (this.currentMode === 'review') {
            this.startReviewMode();
        }
    }

    /**
     * 返回主菜单
     */
    backToMenu() {
        // 清除倒计时
        this.clearAutoNextCountdown();

        document.getElementById('quizContainer').classList.add('hidden');
        document.getElementById('completionScreen').classList.add('hidden');
        document.getElementById('modeSelection').classList.remove('hidden');
        document.getElementById('wordlistManager').classList.remove('hidden');
    }

    /**
     * 处理键盘事件
     */
    handleKeyPress(event) {
        if (document.getElementById('quizContainer').classList.contains('hidden')) return;

        if (event.key === 'Enter') {
            if (!document.getElementById('submitBtn').classList.contains('hidden')) {
                this.submitAnswer();
            }
            // 移除手动按Enter切换下一题的功能，因为现在是自动切换
        }
    }

    /**
     * 打乱数组
     */
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
}

// 创建全局测验管理器实例
window.quizManager = new QuizManager();
