/**
 * 学习模式模块
 * 处理拼写模式、选择题模式和错题复习功能
 */

class QuizManager {
    constructor() {
        this.currentMode = null;
        this.words = [];
        this.currentWordIndex = 0;
        this.currentWord = null;
        this.correctCount = 0;
        this.wrongCount = 0;
        this.wrongWords = [];
        this.startTime = null;
        this.usedIndexes = [];
        this.countdownTimer = null;

        this.initEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 模式选择按钮
        document.getElementById('spellingModeBtn').addEventListener('click', () => this.startSpellingMode());
        document.getElementById('choiceModeBtn').addEventListener('click', () => this.startChoiceMode());
        document.getElementById('reviewModeBtn').addEventListener('click', () => this.startReviewMode());
        document.getElementById('spencerModeBtn').addEventListener('click', () => this.startSpencerMode());
        
        // 学习界面按钮
        document.getElementById('submitBtn').addEventListener('click', () => this.submitAnswer());
        document.getElementById('nextBtn').addEventListener('click', () => this.nextQuestion());
        document.getElementById('backToMenuBtn').addEventListener('click', () => this.backToMenu());
        
        // 完成界面按钮
        document.getElementById('restartBtn').addEventListener('click', () => this.restartQuiz());
        document.getElementById('reviewErrorsBtn').addEventListener('click', () => this.showReviewSelection());
        document.getElementById('backHomeBtn').addEventListener('click', () => this.backToMenu());

        // 复习选择界面按钮
        document.getElementById('startSelectedReviewBtn').addEventListener('click', () => this.startSelectedReview());
        document.getElementById('cancelReviewSelectionBtn').addEventListener('click', () => this.backToMenu());
        
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
    }

    /**
     * 开始拼写模式
     */
    async startSpellingMode() {
        if (!this.validateWordlist()) return;
        
        this.currentMode = 'spelling';
        this.initQuiz();
        this.showQuizContainer();
        this.nextQuestion();
    }

    /**
     * 开始选择题模式
     */
    async startChoiceMode() {
        if (!this.validateWordlist()) return;
        
        if (this.words.length < 4) {
            wordlistManager.showMessage('选择题模式至少需要4个单词', 'error');
            return;
        }
        
        this.currentMode = 'choice';
        this.initQuiz();
        this.showQuizContainer();
        this.nextQuestion();
    }

    /**
     * 显示复习选择界面
     */
    async showReviewSelection() {
        try {
            const wrongWords = await wordDB.getWrongWords();
            if (wrongWords.length === 0) {
                wordlistManager.showMessage('暂无错题需要复习', 'info');
                return;
            }

            // 存储所有错题数据供筛选使用
            this.allWrongWords = wrongWords;

            // 显示复习选择界面
            document.getElementById('modeSelection').classList.add('hidden');
            document.getElementById('wordlistManager').classList.add('hidden');
            document.getElementById('quizContainer').classList.add('hidden');
            document.getElementById('completionScreen').classList.add('hidden');
            document.getElementById('reviewSelectionScreen').classList.remove('hidden');

            // 初始化复习选择界面
            this.initReviewSelection();
        } catch (error) {
            console.error('加载错题失败:', error);
            wordlistManager.showMessage('加载错题失败', 'error');
        }
    }

    /**
     * 初始化复习选择界面
     */
    initReviewSelection() {
        // 添加筛选条件变化监听
        const filterElements = document.querySelectorAll('input[name="masteryLevel"], input[name="wrongCount"], input[name="reviewStatus"], #reviewLimit');
        filterElements.forEach(element => {
            element.addEventListener('change', () => this.updateReviewPreview());
        });

        // 添加预览展开/收起功能
        const toggleBtn = document.getElementById('togglePreviewBtn');
        const previewContent = document.getElementById('wordPreviewContent');

        toggleBtn.addEventListener('click', () => {
            if (previewContent.classList.contains('hidden')) {
                previewContent.classList.remove('hidden');
                toggleBtn.textContent = '收起';
            } else {
                previewContent.classList.add('hidden');
                toggleBtn.textContent = '展开查看';
            }
        });

        // 添加快速选择预设功能
        const presetBtns = document.querySelectorAll('.preset-btn');
        presetBtns.forEach(btn => {
            btn.addEventListener('click', () => this.applyPreset(btn.dataset.preset));
        });

        // 添加全选功能
        const selectAllCheckbox = document.getElementById('selectAllWords');
        const clearSelectionBtn = document.getElementById('clearSelectionBtn');
        const selectDifficultBtn = document.getElementById('selectDifficultBtn');

        selectAllCheckbox.addEventListener('change', () => this.toggleSelectAll());
        clearSelectionBtn.addEventListener('click', () => this.clearWordSelection());
        selectDifficultBtn.addEventListener('click', () => this.selectDifficultWords());

        // 初始化选中的单词数组
        this.selectedWords = [];

        // 初始预览
        this.updateReviewPreview();
    }

    /**
     * 更新复习预览
     */
    updateReviewPreview() {
        const filteredWords = this.getFilteredWords();
        document.getElementById('previewCount').textContent = filteredWords.length;

        // 清理无效的选择（筛选后不存在的单词）
        this.cleanupInvalidSelections(filteredWords);

        // 更新单词列表预览
        this.updateWordPreviewList(filteredWords);

        // 显示/隐藏预览列表
        const wordPreviewList = document.getElementById('wordPreviewList');
        if (filteredWords.length === 0) {
            wordPreviewList.classList.add('hidden');
            this.selectedWords = []; // 清空选择
        } else {
            wordPreviewList.classList.remove('hidden');
        }

        // 更新选择UI
        this.updateSelectionUI();
    }

    /**
     * 清理无效的选择
     */
    cleanupInvalidSelections(filteredWords) {
        const validWordIds = filteredWords.map(word => word.id || `word_${filteredWords.indexOf(word)}`);
        this.selectedWords = this.selectedWords.filter(wordId => validWordIds.includes(wordId));
    }

    /**
     * 更新单词预览列表
     */
    updateWordPreviewList(words) {
        const previewItems = document.getElementById('wordPreviewItems');
        const toggleBtn = document.getElementById('togglePreviewBtn');
        const previewContent = document.getElementById('wordPreviewContent');

        if (words.length === 0) {
            previewItems.innerHTML = '';
            return;
        }

        // 限制预览显示的单词数量，避免列表过长
        const displayWords = words.slice(0, 20);
        const hasMore = words.length > 20;

        previewItems.innerHTML = displayWords.map((word, index) => {
            const masteryLevel = word.masteryLevel || 0;
            const wrongCount = word.wrongCount || 1;
            const nextReviewDate = new Date(word.nextReviewDate || new Date());
            const isOverdue = nextReviewDate <= new Date();
            const wordId = word.id || `word_${index}`;
            const isSelected = this.selectedWords.includes(wordId);

            return `
                <div class="word-preview-item flex items-center p-3 bg-gray-50 rounded-lg ${isSelected ? 'ring-2 ring-purple-300 bg-purple-50' : ''}">
                    <label class="flex items-center cursor-pointer flex-1">
                        <input type="checkbox"
                               class="word-checkbox mr-3 filter-checkbox"
                               data-word-id="${wordId}"
                               data-word-index="${index}"
                               ${isSelected ? 'checked' : ''}
                               onchange="quizManager.toggleWordSelection('${wordId}', ${index})">
                        <div class="flex items-center justify-between flex-1">
                            <div class="flex items-center space-x-3">
                                <span class="mastery-indicator mastery-${masteryLevel}">级别${masteryLevel}</span>
                                <div>
                                    <span class="font-medium text-gray-800">${word.chinese}</span>
                                    <span class="text-gray-400 mx-2">→</span>
                                    <span class="text-blue-600 font-medium">${word.english}</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="word-status-tag status-error-count">错${wrongCount}次</span>
                                ${isOverdue ?
                                    '<span class="word-status-tag status-overdue">需复习</span>' :
                                    '<span class="word-status-tag status-learning">学习中</span>'
                                }
                            </div>
                        </div>
                    </label>
                </div>
            `;
        }).join('');

        // 如果有更多单词，显示提示
        if (hasMore) {
            previewItems.innerHTML += `
                <div class="text-center text-sm text-gray-500 py-2 border-t border-gray-200">
                    还有 ${words.length - 20} 个单词未显示...
                </div>
            `;
        }

        // 重置展开状态
        previewContent.classList.add('hidden');
        toggleBtn.textContent = '展开查看';

        // 更新选择状态
        this.updateSelectionUI();
    }

    /**
     * 切换单词选择状态
     */
    toggleWordSelection(wordId, wordIndex) {
        const filteredWords = this.getFilteredWords();
        const word = filteredWords[wordIndex];

        if (!word) return;

        const index = this.selectedWords.indexOf(wordId);
        if (index > -1) {
            // 取消选择
            this.selectedWords.splice(index, 1);
        } else {
            // 添加选择
            this.selectedWords.push(wordId);
        }

        this.updateSelectionUI();
        this.updateWordItemStyle(wordId);
    }

    /**
     * 全选/取消全选
     */
    toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllWords');
        const filteredWords = this.getFilteredWords();

        if (selectAllCheckbox.checked) {
            // 全选
            this.selectedWords = filteredWords.map(word => word.id || `word_${filteredWords.indexOf(word)}`);
        } else {
            // 取消全选
            this.selectedWords = [];
        }

        this.updateSelectionUI();
        this.updateAllWordItemStyles();
    }

    /**
     * 清空选择
     */
    clearWordSelection() {
        this.selectedWords = [];
        document.getElementById('selectAllWords').checked = false;
        this.updateSelectionUI();
        this.updateAllWordItemStyles();
    }

    /**
     * 选择困难单词（错误次数>=3次或掌握程度<=1级）
     */
    selectDifficultWords() {
        const filteredWords = this.getFilteredWords();
        this.selectedWords = [];

        filteredWords.forEach((word, index) => {
            const wrongCount = word.wrongCount || 1;
            const masteryLevel = word.masteryLevel || 0;

            // 困难单词的条件：错误次数>=3次 或 掌握程度<=1级
            if (wrongCount >= 3 || masteryLevel <= 1) {
                const wordId = word.id || `word_${index}`;
                this.selectedWords.push(wordId);
            }
        });

        this.updateSelectionUI();
        this.updateAllWordItemStyles();

        // 视觉反馈
        if (this.selectedWords.length > 0) {
            wordlistManager.showMessage(`已选择 ${this.selectedWords.length} 个困难单词`, 'success');
        } else {
            wordlistManager.showMessage('当前筛选结果中没有困难单词', 'info');
        }
    }

    /**
     * 更新选择UI状态
     */
    updateSelectionUI() {
        const selectedCount = this.selectedWords.length;
        const filteredWords = this.getFilteredWords();
        const totalCount = filteredWords.length;

        // 更新选择计数
        document.getElementById('selectedCount').textContent = selectedCount;

        // 更新全选复选框状态
        const selectAllCheckbox = document.getElementById('selectAllWords');
        if (selectedCount === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (selectedCount === totalCount) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }

        // 更新开始按钮状态
        const startBtn = document.getElementById('startSelectedReviewBtn');
        if (selectedCount === 0) {
            startBtn.disabled = true;
            startBtn.classList.add('opacity-50', 'cursor-not-allowed');
            startBtn.innerHTML = '<i class="fas fa-play mr-2"></i>请选择单词';
        } else {
            startBtn.disabled = false;
            startBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            startBtn.innerHTML = `<i class="fas fa-play mr-2"></i>开始复习 (${selectedCount}个)`;
        }
    }

    /**
     * 更新单个单词项的样式
     */
    updateWordItemStyle(wordId) {
        const checkbox = document.querySelector(`input[data-word-id="${wordId}"]`);
        if (!checkbox) return;

        const wordItem = checkbox.closest('.word-preview-item');
        const isSelected = this.selectedWords.includes(wordId);

        checkbox.checked = isSelected;

        if (isSelected) {
            wordItem.classList.add('ring-2', 'ring-purple-300', 'bg-purple-50');
            wordItem.classList.remove('bg-gray-50');
        } else {
            wordItem.classList.remove('ring-2', 'ring-purple-300', 'bg-purple-50');
            wordItem.classList.add('bg-gray-50');
        }
    }

    /**
     * 更新所有单词项的样式
     */
    updateAllWordItemStyles() {
        const checkboxes = document.querySelectorAll('.word-checkbox');
        checkboxes.forEach(checkbox => {
            const wordId = checkbox.dataset.wordId;
            this.updateWordItemStyle(wordId);
        });
    }

    /**
     * 根据筛选条件获取错题
     */
    getFilteredWords() {
        if (!this.allWrongWords) return [];

        const now = new Date();
        let filteredWords = [...this.allWrongWords];

        // 掌握程度筛选
        const selectedMasteryLevels = Array.from(document.querySelectorAll('input[name="masteryLevel"]:checked'))
            .map(input => parseInt(input.value));
        if (selectedMasteryLevels.length > 0) {
            filteredWords = filteredWords.filter(word =>
                selectedMasteryLevels.includes(word.masteryLevel || 0)
            );
        }

        // 错误次数筛选
        const selectedWrongCounts = Array.from(document.querySelectorAll('input[name="wrongCount"]:checked'))
            .map(input => input.value);
        if (selectedWrongCounts.length > 0) {
            filteredWords = filteredWords.filter(word => {
                const wrongCount = word.wrongCount || 1;
                return selectedWrongCounts.some(range => {
                    if (range === '1-2') return wrongCount >= 1 && wrongCount <= 2;
                    if (range === '3-5') return wrongCount >= 3 && wrongCount <= 5;
                    if (range === '6+') return wrongCount >= 6;
                    return false;
                });
            });
        }

        // 复习状态筛选
        const selectedReviewStatus = Array.from(document.querySelectorAll('input[name="reviewStatus"]:checked'))
            .map(input => input.value);
        if (selectedReviewStatus.length > 0 && !selectedReviewStatus.includes('all')) {
            filteredWords = filteredWords.filter(word => {
                const nextReviewDate = new Date(word.nextReviewDate || now);
                const isDue = nextReviewDate <= now;
                const isLearning = !isDue && (word.masteryLevel || 0) < 5;

                return selectedReviewStatus.some(status => {
                    if (status === 'due') return isDue;
                    if (status === 'learning') return isLearning;
                    return false;
                });
            });
        }

        // 数量限制
        const limit = document.getElementById('reviewLimit').value;
        if (limit !== 'all') {
            const limitNum = parseInt(limit);
            // 按优先级排序：掌握程度低的优先，然后按下次复习时间
            filteredWords.sort((a, b) => {
                if ((a.masteryLevel || 0) !== (b.masteryLevel || 0)) {
                    return (a.masteryLevel || 0) - (b.masteryLevel || 0);
                }
                return new Date(a.nextReviewDate || now) - new Date(b.nextReviewDate || now);
            });
            filteredWords = filteredWords.slice(0, limitNum);
        }

        return filteredWords;
    }

    /**
     * 开始选定的复习
     */
    async startSelectedReview() {
        // 检查是否有选中的单词
        if (this.selectedWords.length === 0) {
            wordlistManager.showMessage('请先选择要复习的单词', 'error');
            return;
        }

        const filteredWords = this.getFilteredWords();

        // 只获取选中的单词
        const selectedWordsData = filteredWords.filter(word => {
            const wordId = word.id || `word_${filteredWords.indexOf(word)}`;
            return this.selectedWords.includes(wordId);
        });

        if (selectedWordsData.length === 0) {
            wordlistManager.showMessage('选中的单词无效，请重新选择', 'error');
            return;
        }

        // 获取选择的复习模式
        const reviewMode = document.querySelector('input[name="reviewMode"]:checked').value;

        this.words = selectedWordsData.map(item => ({
            chinese: item.chinese,
            english: item.english,
            id: item.id,
            masteryLevel: item.masteryLevel || 0,
            interval: item.interval || 1,
            wrongCount: item.wrongCount || 1
        }));

        this.currentMode = reviewMode === 'spencer' ? 'spencer' : 'review';
        this.initQuiz();
        this.showQuizContainer();

        if (reviewMode === 'spencer') {
            this.showSpencerInfo();
        }

        // 显示选择信息
        wordlistManager.showMessage(`开始复习 ${selectedWordsData.length} 个选中的单词`, 'success');

        this.nextQuestion();
    }

    /**
     * 应用快速选择预设
     */
    applyPreset(preset) {
        // 清除所有选择
        document.querySelectorAll('input[name="masteryLevel"]').forEach(input => input.checked = false);
        document.querySelectorAll('input[name="wrongCount"]').forEach(input => input.checked = false);
        document.querySelectorAll('input[name="reviewStatus"]').forEach(input => input.checked = false);

        switch (preset) {
            case 'difficult':
                // 困难单词：错误次数多，掌握程度低
                document.querySelector('input[name="masteryLevel"][value="0"]').checked = true;
                document.querySelector('input[name="masteryLevel"][value="1"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="3-5"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="6+"]').checked = true;
                document.querySelector('input[name="reviewStatus"][value="all"]').checked = true;
                document.getElementById('reviewLimit').value = '20';
                break;

            case 'due':
                // 需要复习：到期的单词
                document.querySelector('input[name="masteryLevel"][value="0"]').checked = true;
                document.querySelector('input[name="masteryLevel"][value="1"]').checked = true;
                document.querySelector('input[name="masteryLevel"][value="2"]').checked = true;
                document.querySelector('input[name="masteryLevel"][value="3"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="1-2"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="3-5"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="6+"]').checked = true;
                document.querySelector('input[name="reviewStatus"][value="due"]').checked = true;
                document.getElementById('reviewLimit').value = '30';
                break;

            case 'beginner':
                // 初学单词：掌握程度低，错误次数少
                document.querySelector('input[name="masteryLevel"][value="0"]').checked = true;
                document.querySelector('input[name="masteryLevel"][value="1"]').checked = true;
                document.querySelector('input[name="masteryLevel"][value="2"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="1-2"]').checked = true;
                document.querySelector('input[name="wrongCount"][value="3-5"]').checked = true;
                document.querySelector('input[name="reviewStatus"][value="all"]').checked = true;
                document.getElementById('reviewLimit').value = '20';
                break;

            case 'all':
                // 全部错题
                document.querySelectorAll('input[name="masteryLevel"]').forEach(input => input.checked = true);
                document.querySelectorAll('input[name="wrongCount"]').forEach(input => input.checked = true);
                document.querySelector('input[name="reviewStatus"][value="all"]').checked = true;
                document.getElementById('reviewLimit').value = 'all';
                break;
        }

        // 更新预览
        this.updateReviewPreview();

        // 视觉反馈
        const clickedBtn = document.querySelector(`[data-preset="${preset}"]`);
        clickedBtn.classList.add('ring-2', 'ring-offset-2');
        setTimeout(() => {
            clickedBtn.classList.remove('ring-2', 'ring-offset-2');
        }, 1000);
    }

    /**
     * 开始错题复习模式（保留原有方法用于兼容）
     */
    async startReviewMode() {
        this.showReviewSelection();
    }

    /**
     * 开始斯宾塞复习模式（间隔重复）
     */
    async startSpencerMode() {
        try {
            const wordsForReview = await wordDB.getWordsForReview();
            if (wordsForReview.length === 0) {
                // 显示复习统计信息
                const stats = await wordDB.getWrongWordsStats();
                this.showSpencerStats(stats);
                return;
            }

            this.words = wordsForReview.map(item => ({
                chinese: item.chinese,
                english: item.english,
                id: item.id,
                masteryLevel: item.masteryLevel,
                interval: item.interval,
                wrongCount: item.wrongCount
            }));

            this.currentMode = 'spencer';
            this.initQuiz();
            this.showQuizContainer();
            this.showSpencerInfo();
            this.nextQuestion();
        } catch (error) {
            console.error('加载斯宾塞复习失败:', error);
            wordlistManager.showMessage('加载斯宾塞复习失败', 'error');
        }
    }

    /**
     * 验证词表
     */
    validateWordlist() {
        if (!wordlistManager.hasWords()) {
            wordlistManager.showMessage('请先输入单词内容', 'error');
            return false;
        }
        return true;
    }

    /**
     * 初始化测验
     */
    initQuiz() {
        if (this.currentMode !== 'review') {
            this.words = wordlistManager.getCurrentWords();
        }
        
        this.currentWordIndex = 0;
        this.correctCount = 0;
        this.wrongCount = 0;
        this.wrongWords = [];
        this.startTime = Date.now();
        this.usedIndexes = [];
        
        // 打乱单词顺序
        this.shuffleArray(this.words);
        
        this.updateStats();
        this.updateProgress();
    }

    /**
     * 显示测验容器
     */
    showQuizContainer() {
        document.getElementById('modeSelection').classList.add('hidden');
        document.getElementById('wordlistManager').classList.add('hidden');
        document.getElementById('quizContainer').classList.remove('hidden');
        document.getElementById('completionScreen').classList.add('hidden');
    }

    /**
     * 下一题
     */
    nextQuestion() {
        // 清除可能存在的倒计时
        this.clearAutoNextCountdown();

        if (this.usedIndexes.length >= this.words.length) {
            this.completeQuiz();
            return;
        }

        // 随机选择未使用的单词
        let wordIndex;
        do {
            wordIndex = Math.floor(Math.random() * this.words.length);
        } while (this.usedIndexes.includes(wordIndex));

        this.usedIndexes.push(wordIndex);
        this.currentWord = this.words[wordIndex];
        this.currentWordIndex = this.usedIndexes.length;

        this.updateProgress();
        this.clearResult();

        if (this.currentMode === 'spelling' || this.currentMode === 'review') {
            this.showSpellingQuestion();
        } else if (this.currentMode === 'choice') {
            this.showChoiceQuestion();
        }
    }

    /**
     * 显示拼写题目
     */
    showSpellingQuestion() {
        document.getElementById('questionText').textContent = `请输入"${this.currentWord.chinese}"的英文：`;
        
        const answerArea = document.getElementById('answerArea');
        answerArea.innerHTML = `
            <input type="text" id="spellingInput" 
                   class="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none text-center"
                   placeholder="输入英文单词" autocomplete="off">
        `;
        
        document.getElementById('submitBtn').classList.remove('hidden');
        document.getElementById('nextBtn').classList.add('hidden');
        
        // 聚焦输入框
        setTimeout(() => {
            document.getElementById('spellingInput').focus();
        }, 100);
    }

    /**
     * 显示选择题目
     */
    showChoiceQuestion() {
        document.getElementById('questionText').textContent = `"${this.currentWord.chinese}"的英文是：`;
        
        // 生成选项
        const options = [this.currentWord.english];
        while (options.length < 4) {
            const randomWord = this.words[Math.floor(Math.random() * this.words.length)];
            if (!options.includes(randomWord.english)) {
                options.push(randomWord.english);
            }
        }
        
        this.shuffleArray(options);
        
        const answerArea = document.getElementById('answerArea');
        answerArea.innerHTML = options.map((option, index) => `
            <button class="choice-option w-full p-4 mb-3 text-left border-2 border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
                    data-answer="${option}">
                ${String.fromCharCode(65 + index)}. ${option}
            </button>
        `).join('');
        
        // 添加选项点击事件
        document.querySelectorAll('.choice-option').forEach(btn => {
            btn.addEventListener('click', (e) => this.selectChoice(e.target));
        });
        
        document.getElementById('submitBtn').classList.add('hidden');
        document.getElementById('nextBtn').classList.add('hidden');
    }

    /**
     * 选择选项
     */
    selectChoice(button) {
        // 防止重复点击
        if (button.disabled) return;

        // 禁用所有选项
        document.querySelectorAll('.choice-option').forEach(btn => {
            btn.disabled = true;
        });

        // 移除其他选项的选中状态
        document.querySelectorAll('.choice-option').forEach(btn => {
            btn.classList.remove('bg-blue-500', 'text-white', 'border-blue-500');
            btn.classList.add('border-gray-300');
        });

        // 标记当前选项为选中
        button.classList.add('bg-blue-500', 'text-white', 'border-blue-500');
        button.classList.remove('border-gray-300');

        // 立即提交答案（不需要额外延迟，因为submitAnswer中已有0.5s延迟）
        this.submitAnswer(button.dataset.answer);
    }

    /**
     * 提交答案
     */
    async submitAnswer(choiceAnswer = null) {
        let userAnswer;

        if (this.currentMode === 'choice') {
            userAnswer = choiceAnswer;
        } else {
            const input = document.getElementById('spellingInput');
            if (!input) return;
            userAnswer = input.value.trim().toLowerCase();
        }

        if (!userAnswer) {
            wordlistManager.showMessage('请输入答案', 'error');
            return;
        }

        const correctAnswer = this.currentWord.english.toLowerCase();
        const isCorrect = userAnswer === correctAnswer;

        if (isCorrect) {
            this.correctCount++;
            this.showResult('正确！', 'success', this.currentWord);

            // 如果是斯宾塞模式，更新复习记录
            if (this.currentMode === 'spencer' && this.currentWord.id) {
                try {
                    await wordDB.updateWrongWordReview(this.currentWord.id, true, 4);
                } catch (error) {
                    console.error('更新斯宾塞复习记录失败:', error);
                }
            }
        } else {
            this.wrongCount++;
            this.wrongWords.push(this.currentWord);

            if (this.currentMode === 'spencer' && this.currentWord.id) {
                // 斯宾塞模式：更新复习记录
                try {
                    await wordDB.updateWrongWordReview(this.currentWord.id, false, 1);
                } catch (error) {
                    console.error('更新斯宾塞复习记录失败:', error);
                }
            } else {
                // 普通模式：添加到错题本
                try {
                    await wordDB.addWrongWord(1, this.currentWord.chinese, this.currentWord.english);
                } catch (error) {
                    console.error('添加错题失败:', error);
                }
            }

            this.showResult('错误！', 'error', this.currentWord);
        }

        this.updateStats();

        // 禁用输入
        if (this.currentMode !== 'choice') {
            document.getElementById('spellingInput').disabled = true;
        }

        document.getElementById('submitBtn').classList.add('hidden');
        document.getElementById('nextBtn').classList.remove('hidden');

        // 显示倒计时进度条，答对1.5秒，答错3秒
        this.showAutoNextCountdown(isCorrect);
    }

    /**
     * 显示结果
     */
    showResult(message, type, word) {
        const resultArea = document.getElementById('resultArea');
        const isCorrect = type === 'success';

        resultArea.innerHTML = `
            <div class="p-6 rounded-xl ${isCorrect ? 'bg-green-50 border-2 border-green-200 result-success' : 'bg-red-50 border-2 border-red-200 result-error'} text-center">
                <!-- 结果图标和状态 -->
                <div class="mb-6">
                    <i class="fas ${isCorrect ? 'fa-check-circle' : 'fa-times-circle'} text-7xl ${isCorrect ? 'text-green-500' : 'text-red-500'} mb-4 drop-shadow-lg"></i>
                    <div class="text-3xl font-bold ${isCorrect ? 'text-green-800' : 'text-red-800'} mb-2">
                        ${message}
                    </div>
                    ${isCorrect ?
                        '<div class="text-green-600 text-lg">太棒了！继续加油！</div>' :
                        '<div class="text-red-600 text-lg">没关系，继续努力！</div>'
                    }
                </div>

                <!-- 单词信息 -->
                <div class="word-display bg-white rounded-xl p-6 mb-6 shadow-lg">
                    <div class="text-xl font-medium text-gray-700 mb-3">
                        <i class="fas fa-language mr-2 text-blue-500"></i>
                        <span class="text-blue-600 text-2xl font-semibold">${word.chinese}</span>
                        ${this.currentMode === 'spencer' && word.masteryLevel !== undefined ? `
                            <span class="mastery-indicator mastery-${word.masteryLevel} ml-3">
                                级别 ${word.masteryLevel}
                            </span>
                        ` : ''}
                    </div>
                    <div class="text-3xl font-bold ${isCorrect ? 'text-green-600' : 'text-red-600'} mb-2">
                        <i class="fas fa-globe mr-2"></i>
                        ${word.english}
                    </div>
                    ${this.currentMode === 'spencer' && word.interval !== undefined ? `
                        <div class="text-sm text-gray-500 mb-2">
                            <i class="fas fa-clock mr-1"></i>
                            复习间隔: ${word.interval} 天 | 错误次数: ${word.wrongCount || 0}
                        </div>
                    ` : ''}
                    ${!isCorrect ?
                        '<div class="text-red-500 font-medium mt-3 p-2 bg-red-50 rounded-lg">💡 请仔细记住这个单词的拼写</div>' :
                        this.currentMode === 'spencer' ?
                            '<div class="text-green-500 font-medium mt-3">🧠 记忆强化成功！下次复习间隔将延长</div>' :
                            '<div class="text-green-500 font-medium mt-3">✨ 记忆得很好！</div>'
                    }
                </div>

                <!-- 弱化的倒计时显示 -->
                <div class="mt-4 opacity-50">
                    <div class="flex items-center justify-center text-sm text-gray-400 mb-2">
                        <i class="fas fa-clock mr-1"></i>
                        <span id="countdownText">${isCorrect ? '1.5' : '3.0'}s</span>
                        <span class="ml-1">后自动切换</span>
                    </div>
                    <div class="w-24 bg-gray-200 rounded-full h-1 cursor-pointer mx-auto" onclick="quizManager.nextQuestion()" title="点击立即切换">
                        <div id="countdownBar" class="bg-gray-400 h-1 rounded-full transition-all duration-100" style="width: 100%"></div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">点击可跳过</div>
                </div>
            </div>
        `;
    }

    /**
     * 显示自动切换倒计时
     */
    showAutoNextCountdown(isCorrect = true) {
        // 清除之前的倒计时
        this.clearAutoNextCountdown();

        const countdownDuration = isCorrect ? 1500 : 3000; // 答对1.5秒，答错3秒
        const updateInterval = 100; // 每100ms更新一次
        let remainingTime = countdownDuration;

        const countdownText = document.getElementById('countdownText');
        const countdownBar = document.getElementById('countdownBar');

        const updateCountdown = () => {
            remainingTime -= updateInterval;
            const progress = (remainingTime / countdownDuration) * 100;
            const seconds = (remainingTime / 1000).toFixed(1);

            if (countdownText) {
                countdownText.textContent = `${seconds}s`;
            }
            if (countdownBar) {
                countdownBar.style.width = `${Math.max(0, progress)}%`;
            }

            if (remainingTime <= 0) {
                this.countdownTimer = null;
                this.nextQuestion();
            } else {
                this.countdownTimer = setTimeout(updateCountdown, updateInterval);
            }
        };

        this.countdownTimer = setTimeout(updateCountdown, updateInterval);
    }

    /**
     * 清除自动切换倒计时
     */
    clearAutoNextCountdown() {
        if (this.countdownTimer) {
            clearTimeout(this.countdownTimer);
            this.countdownTimer = null;
        }
    }

    /**
     * 清除结果显示
     */
    clearResult() {
        document.getElementById('resultArea').innerHTML = '';
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        document.getElementById('correctCount').textContent = this.correctCount;
        document.getElementById('wrongCount').textContent = this.wrongCount;
        
        const total = this.correctCount + this.wrongCount;
        const accuracy = total > 0 ? Math.round((this.correctCount / total) * 100) : 0;
        document.getElementById('accuracyRate').textContent = `${accuracy}%`;
    }

    /**
     * 更新进度
     */
    updateProgress() {
        const total = this.words.length;
        const current = this.usedIndexes.length;
        const progress = total > 0 ? (current / total) * 100 : 0;
        
        document.getElementById('progressText').textContent = `${current}/${total}`;
        document.getElementById('progressBar').style.width = `${progress}%`;
    }

    /**
     * 完成测验
     */
    async completeQuiz() {
        const totalTime = Date.now() - this.startTime;
        
        // 保存学习记录
        try {
            await wordDB.saveStudyRecord(1, this.currentMode, this.correctCount, this.wrongCount, totalTime);
        } catch (error) {
            console.error('保存学习记录失败:', error);
        }
        
        // 显示完成界面
        this.showCompletionScreen();
    }

    /**
     * 显示完成界面
     */
    showCompletionScreen() {
        document.getElementById('quizContainer').classList.add('hidden');
        document.getElementById('completionScreen').classList.remove('hidden');
        
        const total = this.correctCount + this.wrongCount;
        const accuracy = total > 0 ? Math.round((this.correctCount / total) * 100) : 0;
        
        document.getElementById('finalCorrect').textContent = this.correctCount;
        document.getElementById('finalWrong').textContent = this.wrongCount;
        document.getElementById('finalAccuracy').textContent = `${accuracy}%`;
        
        // 如果没有错题，隐藏复习错题按钮
        if (this.wrongWords.length === 0) {
            document.getElementById('reviewErrorsBtn').classList.add('hidden');
        } else {
            document.getElementById('reviewErrorsBtn').classList.remove('hidden');
        }
    }

    /**
     * 重新开始测验
     */
    restartQuiz() {
        if (this.currentMode === 'spelling') {
            this.startSpellingMode();
        } else if (this.currentMode === 'choice') {
            this.startChoiceMode();
        } else if (this.currentMode === 'review') {
            this.startReviewMode();
        } else if (this.currentMode === 'spencer') {
            this.startSpencerMode();
        }
    }

    /**
     * 返回主菜单
     */
    backToMenu() {
        // 清除倒计时
        this.clearAutoNextCountdown();

        document.getElementById('quizContainer').classList.add('hidden');
        document.getElementById('completionScreen').classList.add('hidden');
        document.getElementById('reviewSelectionScreen').classList.add('hidden');
        document.getElementById('modeSelection').classList.remove('hidden');
        document.getElementById('wordlistManager').classList.remove('hidden');
    }

    /**
     * 处理键盘事件
     */
    handleKeyPress(event) {
        if (document.getElementById('quizContainer').classList.contains('hidden')) return;

        if (event.key === 'Enter') {
            if (!document.getElementById('submitBtn').classList.contains('hidden')) {
                this.submitAnswer();
            }
            // 移除手动按Enter切换下一题的功能，因为现在是自动切换
        }
    }

    /**
     * 显示斯宾塞复习信息
     */
    showSpencerInfo() {
        const infoEl = document.createElement('div');
        infoEl.className = 'bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4';
        infoEl.innerHTML = `
            <div class="flex items-center mb-2">
                <i class="fas fa-brain text-blue-600 mr-2"></i>
                <span class="font-semibold text-blue-800">斯宾塞间隔重复复习</span>
            </div>
            <div class="text-sm text-blue-700">
                根据记忆曲线智能安排复习，提高长期记忆效果
            </div>
        `;

        const quizContainer = document.getElementById('quizContainer');
        const progressSection = quizContainer.querySelector('.mb-6');
        progressSection.after(infoEl);
    }

    /**
     * 显示斯宾塞复习统计
     */
    showSpencerStats(stats) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
                <div class="text-center mb-6">
                    <i class="fas fa-brain text-6xl text-blue-500 mb-4"></i>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">斯宾塞复习统计</h3>
                    <p class="text-gray-600">基于间隔重复算法的智能复习</p>
                </div>

                <div class="space-y-4 mb-6">
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <span class="text-gray-700">总错题数</span>
                        <span class="font-bold text-blue-600">${stats.total}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                        <span class="text-gray-700">需要复习</span>
                        <span class="font-bold text-yellow-600">${stats.needReview}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <span class="text-gray-700">学习中</span>
                        <span class="font-bold text-blue-600">${stats.learning}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <span class="text-gray-700">已掌握</span>
                        <span class="font-bold text-green-600">${stats.mastered}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                        <span class="text-gray-700">困难单词</span>
                        <span class="font-bold text-red-600">${stats.difficult}</span>
                    </div>
                </div>

                <div class="mb-6">
                    <h4 class="font-semibold mb-3">掌握程度分布</h4>
                    <div class="space-y-2">
                        ${stats.byMasteryLevel.map((count, level) => `
                            <div class="flex items-center">
                                <span class="w-16 text-sm text-gray-600">级别${level}</span>
                                <div class="flex-1 bg-gray-200 rounded-full h-2 mx-2">
                                    <div class="bg-gradient-to-r from-red-400 to-green-400 h-2 rounded-full"
                                         style="width: ${stats.total > 0 ? (count / stats.total * 100) : 0}%"></div>
                                </div>
                                <span class="w-8 text-sm font-medium">${count}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                ${stats.needReview > 0 ? `
                    <button onclick="quizManager.startSpencerMode(); this.closest('.fixed').remove();"
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium mb-3">
                        开始复习 (${stats.needReview} 个单词)
                    </button>
                ` : `
                    <div class="text-center p-4 bg-green-50 rounded-lg mb-3">
                        <i class="fas fa-check-circle text-green-500 text-2xl mb-2"></i>
                        <p class="text-green-700 font-medium">今日无需复习</p>
                        <p class="text-green-600 text-sm">明天再来看看吧！</p>
                    </div>
                `}

                <button onclick="this.closest('.fixed').remove();"
                        class="w-full bg-gray-600 hover:bg-gray-700 text-white py-3 px-4 rounded-lg font-medium">
                    关闭
                </button>
            </div>
        `;

        document.body.appendChild(modal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    /**
     * 打乱数组
     */
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
}

// 创建全局测验管理器实例
window.quizManager = new QuizManager();
